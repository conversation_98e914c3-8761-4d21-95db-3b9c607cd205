<script setup lang="tsx">
import VirtualizedTable from '../common/VirtualizedTable.vue';
import TerminalDialog from './TerminalDialog.vue';
import { onMounted, shallowRef, useTemplateRef, computed } from 'vue';
import { TableV2SortOrder, ElMessage, ElMessageBox } from 'element-plus';
import type { ColumnDefinition, RowAction } from '@/types';
import { AdminService } from '@/api';
import { formatDateTime, hasPermission } from '@/script';
import { type MomTerminal, TerminalType } from '../../../../xtrade-sdk/dist';
import { MenuPermitTerminalManagement } from '@/enum';
import { deleteConfirm } from '@/script/interaction';

// 基础列定义
const columns: ColumnDefinition<MomTerminal> = [
  { key: 'terminalName', title: '终端名称', width: 200, sortable: true },
  { key: 'description', title: '描述信息', width: 250, sortable: true },
  {
    key: 'status',
    title: '状态',
    width: 200,
    sortable: true,
    cellRenderer: ({ rowData }: { rowData: MomTerminal }) => {
      return canToggleStatus.value ? (
        <el-switch
          modelValue={rowData.status}
          active-value={1}
          inactive-value={0}
          disabled={!canToggleStatus.value}
          before-change={() => beforeChange(rowData)}
        />
      ) : (
        <span class={rowData.status === 1 ? 'c-[var(--g-green)]' : 'c-[var(--g-red)]'}>
          {rowData.status === 1 ? '启用' : '禁用'}
        </span>
      );
    },
  },
  {
    key: 'interfaceType',
    title: '类型',
    width: 200,
    sortable: true,
    cellRenderer: formatInterfaceType,
  },
  {
    key: 'terminalAccount',
    title: '账号数量',
    width: 120,
    sortable: true,
    cellRenderer: ({ rowData }: { rowData: MomTerminal }) => {
      return (
        <span class="cursor-pointer underline" onClick={() => showAccounts(rowData)}>
          {rowData.terminalAccount}
        </span>
      );
    },
  },
  {
    key: 'createTime',
    title: '创建时间',
    width: 200,
    sortable: true,
    cellRenderer: formatDate,
  },
  {
    key: 'updateTime',
    title: '更新时间',
    width: 200,
    sortable: true,
    cellRenderer: formatDate,
  },
];

// 权限判断
const canCreate = computed(() => hasPermission(MenuPermitTerminalManagement.创建));
const canEdit = computed(() => hasPermission(MenuPermitTerminalManagement.编辑));
const canDelete = computed(() => hasPermission(MenuPermitTerminalManagement.删除));
const canToggleStatus = computed(() => hasPermission(MenuPermitTerminalManagement.终端状态));

// 行操作
const rowActions: RowAction<MomTerminal>[] = [
  {
    label: '编辑',
    icon: 'edit',
    show: () => canEdit.value,
    onClick: row => {
      editRow(row);
    },
  },
  {
    label: '删除',
    icon: 'remove',
    show: () => canDelete.value,
    onClick: row => {
      deleteRow(row);
    },
  },
];

const TerminalTypes = Object.values(TerminalType);
const records = shallowRef<MomTerminal[]>([]);
const tableRef = useTemplateRef('tableRef');

// 对话框相关
const dialogVisible = shallowRef(false);
const editingTerminal = shallowRef<MomTerminal | undefined>();
const accountVisible = shallowRef(false);

function showAccounts(terminal: MomTerminal) {
  editingTerminal.value = terminal;
  accountVisible.value = true;
}

function formatDate(params: any) {
  return <span>{formatDateTime(params.cellData)}</span>;
}

// 格式化接口类型
function formatInterfaceType({ cellData }: { cellData: number }) {
  return <span>{TerminalTypes.find(x => x.Value == cellData)?.Label || cellData}</span>;
}

const beforeChange = async (rowData: MomTerminal) => {
  if (!canToggleStatus.value) {
    ElMessage.error('您没有权限修改终端状态');
    return false;
  }

  const { errorCode, errorMsg } = await AdminService.updateTerminal({
    ...rowData,
    status: rowData.status == 1 ? 0 : 1,
  });
  if (errorCode === 0) {
    rowData.status = rowData.status == 1 ? 0 : 1;
    return true;
  } else {
    ElMessage.error(errorMsg || '操作失败');
    return false;
  }
};

// 新建终端
const handleCreate = () => {
  editingTerminal.value = undefined;
  dialogVisible.value = true;
};

// 编辑终端
function editRow(row: MomTerminal) {
  editingTerminal.value = row;
  dialogVisible.value = true;
}

// 删除终端
async function deleteRow(row: MomTerminal) {
  const result = await deleteConfirm('删除终端', `确定要删除终端"${row.terminalName}"吗？`);
  if (!result) return;
  const { errorCode, errorMsg } = await AdminService.deleteTerminal(row.id);
  if (errorCode === 0) {
    ElMessage.success('删除成功');
    await request();
  } else {
    ElMessage.error(errorMsg || '删除失败');
  }
}

// 对话框成功回调
const handleDialogSuccess = async () => {
  await request();
};

async function request() {
  try {
    records.value = await AdminService.getTerminals();
  } catch (error) {
    console.error('获取终端列表失败:', error);
    ElMessage.error('获取终端列表失败');
  }
}

onMounted(() => {
  request();
});
</script>

<template>
  <VirtualizedTable
    ref="tableRef"
    :sort="{ key: 'createTime', order: TableV2SortOrder.DESC }"
    :columns="columns"
    :data="records"
    :row-actions="rowActions"
    :row-action-width="170"
    select
    fixed
  >
    <template #actions>
      <div class="actions" flex aic>
        <!-- <el-button link size="small" class="typical-text-button">
          <i class="iconfont icon-setting"></i>
          <span>列配置</span>
        </el-button>
        <el-button link size="small" class="typical-text-button">
          <i class="iconfont icon-download"></i>
          <span>下载</span>
        </el-button> -->
        <el-button v-if="canCreate" type="primary" @click="handleCreate">
          <i class="iconfont icon-add-new" mr-5></i>
          <span>新建交易终端</span>
        </el-button>
      </div>
    </template>
  </VirtualizedTable>

  <!-- 终端编辑对话框 -->
  <TerminalDialog
    v-model="dialogVisible"
    :terminal="editingTerminal"
    @success="handleDialogSuccess"
  />
</template>

<style scoped></style>
