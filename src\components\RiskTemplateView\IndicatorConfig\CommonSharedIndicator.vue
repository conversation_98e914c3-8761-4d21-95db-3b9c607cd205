<script setup lang="ts">
import { deepClone, isNone } from '@/script';
import type { RiskRuleBasicInfo } from '@/types/riskc';
import { computed, reactive, useTemplateRef, watch } from 'vue';
import ApplyAssetScope from './ApplyAssetScope.vue';
import { Repos, type RiskRule } from '../../../../../xtrade-sdk/dist';
import BlackAssetKind from './RuleTemplate/BlackAssetKind.vue';
import { ElMessage } from 'element-plus';

const { contextRule } = defineProps<{ contextRule: RiskRule }>();

const formName = reactive({ name: '' });
const rulesName = {
  name: [{ required: true, message: '请输入指标名字', trigger: 'blur' }],
};

const formData = reactive<RiskRuleBasicInfo>({
  effectiveDate: {
    start: '',
    end: '',
  },
  effectiveTime: {
    start: '',
    end: '',
  },
});

const rules = {
  'effectiveDate.start': [{ required: true, message: '请输入开始日期', trigger: 'blur' }],
  'effectiveDate.end': [
    { required: true, message: '请输入结束日期', trigger: 'blur' },
    { validator: checkEndDate, trigger: 'blur' },
  ],
  'effectiveTime.start': [{ required: false, message: '请输入开始时间', trigger: 'blur' }],
  'effectiveTime.end': [
    { required: false, message: '请输入结束时间', trigger: 'blur' },
    { validator: checkEndTime, trigger: 'blur' },
  ],
};

watch(() => contextRule, handleConextChange, { immediate: true });
const ruleDetailConfig = computed(() => contextRule.configuration as any);

const scopeConfig = computed(() => {
  const { kindCodes, overlaies } = contextRule.configuration;
  return { kindCodes, overlaies };
});

function handleConextChange() {
  const { ruleName, beginDay, beginTime, endDay, endTime } = contextRule;
  const fd = formData;
  formName.name = ruleName;

  fd.effectiveDate = {
    start: beginDay.toString(),
    end: endDay.toString(),
  };

  const timeRange = {
    start: beginTime.toString(),
    end: endTime.toString(),
  };

  while (timeRange.start.length < 6) {
    timeRange.start = '0' + timeRange.start;
  }

  while (timeRange.end.length < 6) {
    timeRange.end = '0' + timeRange.end;
  }

  fd.effectiveTime = timeRange;
}

const is4Creation = computed(() => {
  return !contextRule || isNone(contextRule.id);
});

const $form = useTemplateRef('$form');
const $formName = useTemplateRef('$formName');
const $ruleSpec = useTemplateRef('$ruleSpec');
const $scope = useTemplateRef('$scope');

function checkEndDate(rule: any, value: string, callback: (error?: Error) => void) {
  const { start, end } = formData.effectiveDate;
  if (start && end && end < start) {
    callback(new Error('结束日期，大于开始日期'));
  } else if (!end) {
    callback(new Error('请输入结束日期'));
  } else {
    callback();
  }
}

function checkEndTime(rule: any, value: string, callback: (error?: Error) => void) {
  const { start, end } = formData.effectiveTime;
  if (start && end && end <= start) {
    callback(new Error('结束时间，大于等于开始时间'));
  } else {
    callback();
  }
}

const handleValidate = () => {
  $formName.value!.validate().then(() => {
    $form.value!.validate().then(() => {
      $ruleSpec.value!.validate().then(() => {
        $scope.value!.validate().then(() => {
          handleSave();
        });
      });
    });
  });
};

const repoInstance = new Repos.RiskControlRepo();
const emitter = defineEmits<{
  saved: [];
}>();

const handleSave = async () => {
  // 资产分类范围
  const scopeSetting = $scope.value!.getSetting();
  const { kindCodes, overlaies } = scopeSetting;
  // 风控规则详细配置
  const ruleSetting = $ruleSpec.value!.getSetting();
  Object.assign(ruleSetting, { kindCodes, overlaies });
  // 创建规则副本用于创建
  const cloned = deepClone(contextRule);
  // 更新规则名称
  cloned.ruleName = formName.name;
  // 更新其他附属字段
  const { effectiveDate, effectiveTime } = formData;
  cloned.beginDay = Number(effectiveDate.start);
  cloned.endDay = Number(effectiveDate.end);
  cloned.beginTime = Number(effectiveTime.start);
  cloned.endTime = Number(effectiveTime.end);
  // 组合规则的参数配置
  cloned.configuration = {
    effectiveDate,
    effectiveTime,
    kindCodes,
    overlaies: overlaies.map(x => x.variable),
    baseConditions: overlaies.map(x => ({
      name: x.variable,
      expression: x.expression,
      value: x.value,
    })),
  };

  const resp = is4Creation.value
    ? await repoInstance.CreateRule(cloned)
    : await repoInstance.UpdateRule(cloned);
  const { errorCode, errorMsg } = resp;

  if (errorCode == 0) {
    ElMessage.success('保存成功');
    emitter('saved');
  } else {
    ElMessage.error(`保存失败：${errorCode}/${errorMsg}`);
  }
};
</script>

<template>
  <div class="view-idc-panel" h-full pt-5 p-x-12 flex flex-col gap-10>
    <div h-40>
      <el-form ref="$formName" :model="formName" :rules="rulesName" label-width="80px">
        <el-form-item label="指标名称" prop="name">
          <div class="name-row" w-full flex jcsb aic>
            <el-input v-model.trim="formName.name" placeholder="请输入指标名称" clearable />
            <el-button type="primary" @click="handleValidate" ml-10>
              {{ is4Creation ? '创建' : '更新' }}
            </el-button>
            <!-- <el-button>新增</el-button> -->
          </div>
        </el-form-item>
      </el-form>
    </div>
    <div flex-1 flex of-hidden>
      <div h-full w-600 of-y-auto>
        <el-form ref="$form" :model="formData" :rules="rules" label-width="80px">
          <BlackAssetKind ref="$ruleSpec" v-bind:ruleConfig="ruleDetailConfig"></BlackAssetKind>
          <div class="custom-row" flex aic gap-16>
            <div w-270>
              <el-form-item label="生效日期" prop="effectiveDate.start">
                <el-date-picker
                  v-model="formData.effectiveDate.start"
                  type="date"
                  value-format="YYYYMMDD"
                  placeholder="开始日期"
                  clearable
                />
              </el-form-item>
            </div>
            <label class="placed-label">至</label>
            <div w-190 class="post-item">
              <el-form-item label="" prop="effectiveDate.end">
                <el-date-picker
                  v-model="formData.effectiveDate.end"
                  type="date"
                  value-format="YYYYMMDD"
                  placeholder="结束日期"
                  clearable
                />
              </el-form-item>
            </div>
          </div>
          <div class="custom-row" flex aic gap-16>
            <div w-270>
              <el-form-item label="生效时间" prop="effectiveTime.start">
                <el-time-picker
                  v-model="formData.effectiveTime.start"
                  value-format="HHmmss"
                  placeholder="开始时间"
                  clearable
                />
              </el-form-item>
            </div>
            <label class="placed-label">至</label>
            <div w-190 class="post-item">
              <el-form-item label="" prop="effectiveTime.end">
                <el-time-picker
                  v-model="formData.effectiveTime.end"
                  value-format="HHmmss"
                  placeholder="结束时间"
                  clearable
                />
              </el-form-item>
            </div>
          </div>
        </el-form>
      </div>
      <div h-full flex-1 of-hidden>
        <ApplyAssetScope ref="$scope" :scope="scopeConfig"></ApplyAssetScope>
      </div>
    </div>
  </div>
</template>

<style scoped>
.view-idc-panel {
  .placed-label {
    color: var(--g-text-color-2);
  }

  :deep() {
    .el-form-item__label {
      color: var(--g-text-color-2);
    }
  }

  .name-row {
    :deep() {
      > .el-input {
        width: 100px;
        flex-grow: 1;
        flex-shrink: 1;
      }
    }
  }

  .custom-row {
    margin-bottom: 18px;

    :deep() {
      > .el-form-item {
        margin-bottom: 0 !important;
      }
    }
  }

  .post-item {
    :deep() {
      > .el-form-item__content {
        margin-left: 0 !important;
      }
    }
  }
}
</style>

<style>
.view-idc-panel {
  .el-form-item__label {
    height: 40px;
    line-height: 40px;
  }

  .el-select,
  .el-select__wrapper,
  .el-input {
    height: 40px;
  }
}
</style>
