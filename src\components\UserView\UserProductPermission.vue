<script setup lang="ts">
import { ref, computed, onMounted, shallowRef, watch } from 'vue';
import { ElMessage } from 'element-plus';
import {
  Repos,
  type MomUser,
  type LegacyFundInfo,
  type LegacyAccountInfo,
} from '../../../../xtrade-sdk/dist';

const { user } = defineProps<{
  user?: MomUser;
}>();

// 仓库实例
const adminRepo = new Repos.AdminRepo();
const governanceRepo = new Repos.GovernanceRepo();

// 响应式数据
const userProducts = shallowRef<LegacyFundInfo[]>([]);
const allProducts = shallowRef<LegacyFundInfo[]>([]);
const productAccounts = shallowRef<LegacyAccountInfo[]>([]);
const selectedProduct = shallowRef<LegacyFundInfo | null>(null);
const selectedProductForShare = ref<string>('');
const loading = ref(false);
const accountsLoading = ref(false);

// 计算属性：可选择的产品（排除已绑定的）
const availableProducts = computed(() => {
  const userProductIds = userProducts.value.map(p => p.id);
  return allProducts.value.filter(p => !userProductIds.includes(p.id));
});

// 加载分享给用户的产品列表
const loadUserProducts = async () => {
  if (!user?.id) return;

  loading.value = true;
  try {
    const { errorCode, errorMsg, data } = await adminRepo.QueryUserProducts(user.id);
    if (errorCode === 0) {
      userProducts.value = data || [];
    } else {
      ElMessage.error(errorMsg || '加载用户产品失败');
    }
  } catch {
    ElMessage.error('加载用户产品失败');
  } finally {
    loading.value = false;
  }
};

// 加载所有产品列表
const loadAllProducts = async () => {
  try {
    const { errorCode, errorMsg, data } = await governanceRepo.QueryProducts();
    if (errorCode === 0) {
      allProducts.value = data || [];
    } else {
      ElMessage.error(errorMsg || '加载产品列表失败');
    }
  } catch {
    ElMessage.error('加载产品列表失败');
  }
};

// 加载产品账号列表
const loadProductAccounts = async (product: LegacyFundInfo) => {
  accountsLoading.value = true;
  try {
    // 从产品的accounts字段获取账号信息
    if (product.accounts && Array.isArray(product.accounts)) {
      // 这里需要根据账号ID获取完整的账号信息
      const accountIds = product.accounts.map(acc => acc.accountId);
      const { errorCode, errorMsg, data } = await governanceRepo.QueryAccounts();

      if (errorCode === 0 && data) {
        // 过滤出属于当前产品的账号
        productAccounts.value = data.filter(account => accountIds.includes(account.accountId));
      } else {
        ElMessage.error(errorMsg || '加载账号列表失败');
        productAccounts.value = [];
      }
    } else {
      productAccounts.value = [];
    }
  } catch {
    ElMessage.error('加载账号列表失败');
    productAccounts.value = [];
  } finally {
    accountsLoading.value = false;
  }
};

// 选择产品
const handleProductSelect = (product: LegacyFundInfo) => {
  selectedProduct.value = product;
  loadProductAccounts(product);
};

// 分享产品给用户
const handleShareProduct = async () => {
  if (!user?.id || !selectedProductForShare.value) {
    ElMessage.warning('请选择要分享的产品');
    return;
  }

  const product = allProducts.value.find(p => p.id === selectedProductForShare.value);
  if (!product) {
    ElMessage.error('产品不存在');
    return;
  }

  try {
    const { errorCode, errorMsg } = await adminRepo.ShareUserProducts(user.id, [
      {
        id: Number(product.id),
        fundType: product.fundType,
        fundName: product.fundName,
      },
    ]);

    if (errorCode === 0) {
      ElMessage.success('分享成功');
      selectedProductForShare.value = '';
      await loadUserProducts();
    } else {
      ElMessage.error(errorMsg || '分享失败');
    }
  } catch {
    ElMessage.error('分享失败');
  }
};

// 取消分享产品
const handleUnshareProduct = async (product: LegacyFundInfo) => {
  if (!user?.id) return;

  try {
    const { errorCode, errorMsg } = await adminRepo.UnshareUserProducts(user.id, product.id);

    if (errorCode === 0) {
      ElMessage.success('取消分享成功');
      await loadUserProducts();
      // 如果取消分享的是当前选中的产品，清空账号列表
      if (selectedProduct.value?.id === product.id) {
        selectedProduct.value = null;
        productAccounts.value = [];
      }
    } else {
      ElMessage.error(errorMsg || '取消分享失败');
    }
  } catch {
    ElMessage.error('取消分享失败');
  }
};

// 格式化涨跌幅
const formatRisePercent = (value: number) => {
  if (value === 0) return '0.00%';
  const formatted = (value || 0).toFixed(2);
  return value > 0 ? `+${formatted}%` : `${formatted}%`;
};

// 获取涨跌幅颜色类
const getRisePercentClass = (value: number) => {
  if (value > 0) return 'text-red-500';
  if (value < 0) return 'text-green-500';
  return 'text-gray-500';
};

// 监听用户变化
watch(
  () => user?.id,
  newUserId => {
    if (newUserId) {
      loadUserProducts();
      loadAllProducts();
    }
  },
  { immediate: true },
);

// 组件挂载时加载数据
onMounted(() => {
  if (user?.id) {
    loadUserProducts();
    loadAllProducts();
  }
});
</script>

<template>
  <div class="user-product-permission" p-4>
    <!-- 产品选择和分享 -->
    <div class="share-section" mb-4>
      <div flex="~ items-center gap-3">
        <el-select
          v-model="selectedProductForShare"
          placeholder="选择要分享的产品"
          filterable
          clearable
          w-300
        >
          <el-option
            v-for="product in availableProducts"
            :key="product.id"
            :label="product.fundName"
            :value="product.id"
          />
        </el-select>
        <el-button type="primary" :disabled="!selectedProductForShare" @click="handleShareProduct">
          分享产品
        </el-button>
      </div>
    </div>

    <!-- 分享给用户的产品列表 -->
    <div class="user-products-section" mb-6>
      <h3 class="section-title" mb-3>分享给用户的产品</h3>
      <el-table
        :data="userProducts"
        v-loading="loading"
        border
        stripe
        height="300"
        @row-click="handleProductSelect"
        :row-class-name="({ row }) => (selectedProduct?.id === row.id ? 'selected-row' : '')"
      >
        <el-table-column prop="fundName" label="产品名称" width="200" />
        <el-table-column prop="fundType" label="产品类型" width="120" />
        <el-table-column prop="nav" label="净值" width="120">
          <template #default="{ row }">
            {{ (row.nav || 0).toFixed(4) }}
          </template>
        </el-table-column>
        <el-table-column prop="risePercent" label="涨跌幅" width="120">
          <template #default="{ row }">
            <span :class="getRisePercentClass(row.risePercent)">
              {{ formatRisePercent(row.risePercent) }}
            </span>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="120" fixed="right">
          <template #default="{ row }">
            <el-button type="danger" size="small" @click.stop="handleUnshareProduct(row)">
              取消分享
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 产品账号列表 -->
    <div class="product-accounts-section">
      <h3 class="section-title" mb-3>
        产品账号列表
        <span v-if="selectedProduct" class="text-sm text-gray-500 ml-2">
          ({{ selectedProduct.fundName }})
        </span>
      </h3>
      <el-table
        :data="productAccounts"
        v-loading="accountsLoading"
        border
        stripe
        height="300"
        empty-text="请先选择一个产品"
      >
        <el-table-column prop="accountName" label="账号名称" width="200" />
        <el-table-column prop="accountId" label="账号ID" width="150" />
        <el-table-column prop="assetType" label="资产类型" width="120">
          <template #default="{ row }">
            {{ row.assetType === 1 ? '现金' : row.assetType === 2 ? '信用' : '其他' }}
          </template>
        </el-table-column>
        <el-table-column prop="balance" label="总权益" width="120">
          <template #default="{ row }">
            {{ (row.balance || 0).toFixed(2) }}
          </template>
        </el-table-column>
        <el-table-column prop="available" label="可用资金" width="120">
          <template #default="{ row }">
            {{ (row.available || 0).toFixed(2) }}
          </template>
        </el-table-column>
      </el-table>
    </div>
  </div>
</template>

<style scoped>
.user-product-permission {
  .section-title {
    font-size: 16px;
    font-weight: 600;
    color: #303133;
  }

  :deep(.selected-row) {
    background-color: #f0f9ff !important;
  }

  :deep(.selected-row:hover) {
    background-color: #e0f2fe !important;
  }
}
</style>
