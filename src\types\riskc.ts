/**
 * 指标触发项
 */
export interface IndicatorTrigger {
  /**
   * 比较符
   */
  comparer: number;

  /**
   * 阈值
   */
  threshold: number;

  /**
   * 阈值单位
   */
  threholdUnit: string;

  /**
   * 触发项采取动作：1预警 2阻止
   */
  action: number;
}

/**
 * 风控规则基础配置信息
 */
export interface RiskRuleBasicInfo {
  /**
   * 生效日期
   */
  effectiveDate: {
    /**
     * 开始日期
     */
    start: string;

    /**
     * 结束日期
     */
    end: string;
  };

  /**
   * 生效时间
   */
  effectiveTime: {
    /**
     * 开始时间
     */
    start: string;

    /**
     * 结束时间
     */
    end: string;
  };
}

/**
 * 资产作用域设置
 */
export interface AssetScopeSetting {
  /**
   * 资产类别代码
   */
  kindCodes: string[];

  /**
   * 叠加条件
   */
  overlaies: string[];
}

/**
 * 叠加条件
 */
export interface OverlayCondition {
  variable: string;
  description: string;
  expression: number;
  value: number;
}

/**
 * 规模与频度规则模版
 */
export interface RuleScaleAndFrequency extends AssetScopeSetting {
  /**
   * 指标设置
   */
  setting: {
    direction: number;
    triggers: IndicatorTrigger[];
  };

  /**
   * 控制环节
   */
  stepControl: {
    /**
     * 1针对指令 2针对委托
     */
    target: number;

    /**
     * 循环检查频率（单位秒S）
     */
    frequency: number;
  };
}

/**
 * 黑名单风控规则
 */
export interface RuleBlackAssetKind extends AssetScopeSetting {
  /**
   * 触发项采取动作：1预警 2阻止
   */
  action: number;
  /**
   * 控制环节
   */
  stepControl: {
    /**
     * 1针对指令 2针对委托
     */
    target: number;

    /**
     * 循环检查频率（单位秒S）
     */
    frequency: number;
  };
}
