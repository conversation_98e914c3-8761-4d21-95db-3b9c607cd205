<script setup lang="ts">
import { TRADE_DIRECTIONS } from '@/enum';
import type { RuleScaleAndFrequency } from '@/types/riskc';
import { computed, useTemplateRef } from 'vue';

import {
  RISK_TRIGGER_ACTIONS,
  RISK_TRIGGER_COMPARERS,
  RiskStepControls,
  RiskStepControl,
} from '@/enum/riskc';

const props = defineProps<{ ruleConfig: RuleScaleAndFrequency }>();
const ruleConfig = computed(() => props.ruleConfig);

const rules = {
  'setting.direction': [{ required: true, message: '请选择针对交易方向', trigger: 'blur' }],
  'stepControl.target': [{ required: true, message: '请选择风控针对环节', trigger: 'blur' }],
  'stepControl.frequency': [{ required: true, message: '请指定风控频率', trigger: 'blur' }],
  each_comparer: [{ required: true, message: '请选择比较符', trigger: 'blur' }],
  each_threshold: [{ required: true, message: '请输入阈值', trigger: 'blur' }],
  each_action: [{ required: true, message: '请选择处置方式', trigger: 'blur' }],
};

const $form = useTemplateRef('$form');

const frequencyDesc = computed(() => {
  const { target, frequency } = ruleConfig.value.stepControl;
  if (frequency == 0) {
    return target == RiskStepControl.order.value ? '(0s表示委托单还未到柜台)' : '(0s表示事前检测)';
  }

  return '';
});

async function validate() {
  if (!$form.value) {
    return false;
  }
  const result = await $form.value!.validate();
  return result;
}

defineExpose({
  validate,
});
</script>

<template>
  <div class="rule-tmpl" h-full p-x-12>
    <el-form ref="$form" :model="ruleConfig" :rules="rules" label-width="80px">
      <el-form-item label="指标设置" prop="setting.direction">
        <el-select v-model="ruleConfig.setting.direction" placeholder="方向" style="width: 80px">
          <el-option
            v-for="(item, idx) in TRADE_DIRECTIONS"
            :key="idx"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
        <label class="placed-label" p-l-10>单笔最大委托量</label>
      </el-form-item>
      <div
        v-for="(trigger, idx) in ruleConfig.setting.triggers"
        :key="idx"
        class="custom-row"
        flex
        aic
        gap-10
      >
        <div w-170>
          <el-form-item
            label=""
            :prop="`setting.triggers[${idx}].comparer`"
            :rules="rules.each_comparer"
          >
            <el-select v-model="trigger.comparer">
              <el-option
                v-for="(item, idx) in RISK_TRIGGER_COMPARERS"
                :key="idx"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </div>
        <div class="post-item" w-110>
          <el-form-item
            label=""
            :prop="`setting.triggers[${idx}].threshold`"
            :rules="rules.each_threshold"
          >
            <el-input-number
              v-model="trigger.threshold"
              :controls="false"
              :precision="0"
              :min="0"
              placeholder="阈值"
              clearable
            />
          </el-form-item>
        </div>
        <label class="placed-label">万(股/份/张/手)时，</label>
        <div class="post-item" w-80>
          <el-form-item
            label=""
            :prop="`setting.triggers[${idx}].action`"
            :rules="rules.each_action"
          >
            <el-select v-model="trigger.action">
              <el-option
                v-for="(item, idx) in RISK_TRIGGER_ACTIONS"
                :key="idx"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </div>
      </div>
      <div class="custom-row" flex aic gap-10>
        <div w-180>
          <el-form-item label="控制环节" prop="stepControl.target">
            <el-select v-model="ruleConfig.stepControl.target">
              <el-option
                v-for="(item, idx) in RiskStepControls"
                :key="idx"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </div>
        <div class="post-item">
          <el-input-number
            v-model="ruleConfig.stepControl.frequency"
            :controls="false"
            :precision="0"
            :min="0"
            :max="999"
            placeholder="间隔时长"
            style="width: 90px"
            clearable
          />
        </div>
        <label class="placed-label">秒执行一次{{ frequencyDesc }}</label>
      </div>
    </el-form>
  </div>
</template>

<style scoped>
.rule-tmpl {
  .placed-label {
    color: var(--g-text-color-2);
  }

  :deep() {
    .el-form-item__label {
      color: var(--g-text-color-2);
    }
  }

  .custom-row {
    margin-bottom: 18px;

    :deep() {
      > .el-form-item {
        margin-bottom: 0 !important;
      }
    }
  }

  .post-item {
    :deep() {
      > .el-form-item__content {
        margin-left: 0 !important;
      }
    }
  }
}
</style>
