<script setup lang="ts">
import { computed, ref, useTemplateRef, watch } from 'vue';
import type { RuleBlackAssetKind } from '@/types/riskc';
import { RISK_TRIGGER_ACTIONS, RiskStepControl, RiskStepControls } from '@/enum/riskc';
import { deepClone } from '@/script';

const innerConfig = ref<RuleBlackAssetKind>({} as any);
const props = defineProps<{ ruleConfig: RuleBlackAssetKind }>();
watch(
  () => props.ruleConfig,
  () => {
    innerConfig.value = deepClone(props.ruleConfig);
  },
  { immediate: true },
);

const rules = {
  action: [{ required: true, message: '请选择对采取行动', trigger: 'blur' }],
  'stepControl.target': [{ required: true, message: '请选择风控针对环节', trigger: 'blur' }],
  'stepControl.frequency': [{ required: true, message: '请指定风控频率', trigger: 'blur' }],
};

const $form = useTemplateRef('$form');

const frequencyDesc = computed(() => {
  const { target, frequency } = innerConfig.value.stepControl;
  if (frequency == 0) {
    return target == RiskStepControl.order.value ? '(0s表示委托单还未到柜台)' : '(0s表示事前检测)';
  }

  return '';
});

function validate() {
  return $form.value!.validate();
}

function getSetting() {
  return deepClone(innerConfig.value);
}

defineExpose({
  validate,
  getSetting,
});
</script>

<template>
  <div class="rule-tmpl" h-full p-x-12>
    <el-form ref="$form" :model="innerConfig" :rules="rules" label-width="80px">
      <div class="custom-row">
        <el-form-item label="指标设置" prop="action">
          <div flex aic>
            <div w-80>
              <label class="placed-label">黑名单</label>
            </div>
            <el-select v-model="innerConfig.action">
              <el-option
                v-for="(item, idx) in RISK_TRIGGER_ACTIONS"
                :key="idx"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </div>
        </el-form-item>
      </div>
      <div class="custom-row" flex aic gap-10>
        <div w-220>
          <el-form-item label="控制环节" prop="stepControl.target">
            <el-select v-model="innerConfig.stepControl.target">
              <el-option
                v-for="(item, idx) in RiskStepControls"
                :key="idx"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </div>
        <div class="post-item">
          <el-input-number
            v-model="innerConfig.stepControl.frequency"
            :controls="false"
            :precision="0"
            :min="0"
            :max="999"
            placeholder="间隔时长"
            style="width: 90px"
            clearable
          />
        </div>
        <label class="placed-label">秒执行一次{{ frequencyDesc }}</label>
      </div>
    </el-form>
  </div>
</template>

<style scoped>
.rule-tmpl {
  .placed-label {
    color: var(--g-text-color-2);
  }

  :deep() {
    .el-form-item__label {
      color: var(--g-text-color-2);
    }
  }

  .custom-row {
    margin-bottom: 18px;

    :deep() {
      > .el-form-item {
        margin-bottom: 0 !important;
      }
    }
  }

  .post-item {
    :deep() {
      > .el-form-item__content {
        margin-left: 0 !important;
      }
    }
  }
}
</style>
