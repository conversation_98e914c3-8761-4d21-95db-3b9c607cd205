<script setup lang="ts">
import { computed, onMounted, reactive, ref, useTemplateRef, watch } from 'vue';
import { Repos, type KindOfAsset } from '../../../../../xtrade-sdk/dist';
import type { AssetScopeSetting, OverlayCondition } from '@/types/riskc';
import { deepClone, distinct } from '@/script';

interface Kind3rdLevelNode {
  level2Code: string;
  id: number;
  code: string;
  name: string;
  kindInfo: KindOfAsset;
}

interface Kind2ndLevelNode {
  level1Code: string;
  code: string;
  name: string;
  children: Kind3rdLevelNode[];
}

interface Kind1stLevelNode {
  code: string;
  name: string;
  children: Kind2ndLevelNode[];
}

const comparer = {
  gt: { label: '大于', value: 1 },
  eq: { label: '等于', value: 2 },
  lt: { label: '小于', value: 3 },
};

const overlayConditions: OverlayCondition[] = [
  {
    variable: 'stock_flow_market_value',
    description: `股票流通市值${comparer.lt.label}5亿`,
    expression: comparer.lt.value,
    value: 5,
  },
  {
    variable: 'stock_pledge_ration',
    description: `股票质押比率超过50%的股票`,
    expression: comparer.gt.value,
    value: 0.5,
  },
  {
    variable: 'stock_onboard_date',
    description: `上市未满3个月的股票`,
    expression: comparer.lt.value,
    value: 3,
  },
  {
    variable: 'stock_avg_day_trade_amount',
    description: `年日均 5000 万以上的主板、中小板`,
    expression: comparer.gt.value,
    value: 5000,
  },
  {
    variable: 'stock_avg_year_trade_amount',
    description: `年日均 5000 万以下的主板、中小板`,
    expression: comparer.lt.value,
    value: 5000,
  },
];

const flattenList = ref<KindOfAsset[]>([]);
const assets = ref<Kind1stLevelNode[]>([]);
const scopes = computed(() => {
  const matrix = assets.value
    .filter(x => formData.categories.includes(x.code))
    .map(x => x.children);
  return matrix.flat();
});

const repoInstance = new Repos.RiskControlRepo();
const props = defineProps<{ scope: AssetScopeSetting }>();
const states = {
  requiredJob: null as any,
};

watch(
  () => props.scope,
  () => {
    handleContextChange();
  },
  { immediate: true },
);

function handleContextChange() {
  if (assets.value.length > 0) {
    recover();
  } else {
    states.requiredJob = () => {
      recover();
    };
  }
}

function recover() {
  const all = assets.value;
  if (all.length == 0) {
    return;
  }

  const { kindCodes, overlaies } = props.scope;
  // 根据底层代码，溯源其最上层选中的类别
  const level1_codes = flattenList.value
    .filter(x => kindCodes.includes(x.kindCode))
    .map(x => x.parentKindCode);
  const diff_level1_codes = distinct(level1_codes, x => x);

  // 根据底层选中代码，以及向上溯源的选中类别，构建出资产勾选情况
  const cmap: { [level1stCode: string]: string[] } = {};
  all.forEach(ast => {
    const is_level1_choosed = diff_level1_codes.some(x => x == ast.code);
    if (is_level1_choosed) {
      const subset = flattenList.value.filter(x => x.parentKindCode == ast.code);
      cmap[ast.code] = kindCodes.filter(kcode => subset.some(x => x.kindCode == kcode));
    } else {
      cmap[ast.code] = [];
    }
  });

  choicesMap.value = cmap;
  formData.categories = diff_level1_codes;
  formData.overlaies = [...overlaies];
}

const rules = {
  categories: [{ required: true, message: '请选择资产', trigger: 'blur' }],
  overlaies: [{ required: false }],
};

const formData = reactive({
  categories: [] as string[],
  overlaies: [] as string[],
});

/**
 * 资产勾选情况（key/一级资产代码，values/底层资产代码）
 */
const choicesMap = ref<{ [level1stCode: string]: string[] }>({});

function handleCategoryChange() {
  for (const code in choicesMap.value) {
    const is_inside = formData.categories.includes(code);
    if (!is_inside) {
      choicesMap.value[code] = [];
    }
  }
}

function buildLevels(records: KindOfAsset[]): Kind1stLevelNode[] {
  const level_1st_map: { [code: string]: Kind1stLevelNode } = {};
  records.forEach(ast => {
    // check level 1st
    let level1 = level_1st_map[ast.parentKindCode];
    if (level1 === undefined) {
      level1 = {
        code: ast.parentKindCode,
        name: ast.parentKindName,
        children: [],
      };
      level_1st_map[ast.parentKindCode] = level1;
    }

    // check level 2nd
    let level2 = level1.children.find(n => n.code === ast.midKindCode);
    if (level2 === undefined) {
      level2 = {
        level1Code: ast.parentKindCode,
        code: ast.midKindCode,
        name: ast.midKindName,
        children: [],
      };
      level1.children.push(level2);
    }

    // add indicator node
    level2.children.push({
      id: ast.id,
      level2Code: ast.midKindCode,
      code: ast.kindCode,
      name: ast.kindName,
      kindInfo: ast,
    });
  });

  return Object.values(level_1st_map);
}

async function request() {
  const list = (await repoInstance.QueryAssetScopes()).data || [];
  flattenList.value = list;
  assets.value = buildLevels(list);

  if (typeof states.requiredJob === 'function') {
    states.requiredJob();
    states.requiredJob = null;
  }
}

const $form = useTemplateRef('$form');

function getSetting() {
  return {
    kindCodes: Object.values(choicesMap.value).flat(),
    overlaies: deepClone(overlayConditions.filter(x => formData.overlaies.includes(x.variable))),
  };
}

function validate() {
  return $form.value!.validate();
}

defineExpose({
  validate,
  getSetting,
});

onMounted(() => {
  request();
});
</script>

<template>
  <div class="asset-scope-setting" p-10 h-full flex flex-col>
    <div h-100>
      <el-form ref="$form" :model="formData" :rules="rules" label-width="80px">
        <el-form-item label="资产筛选" prop="categories">
          <el-select
            v-model="formData.categories"
            @change="handleCategoryChange"
            placeholder="资产类别"
            multiple
            filterable
            clearable
          >
            <el-option
              v-for="(item, idx) in assets"
              :key="idx"
              :label="item.name"
              :value="item.code"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="条件叠加" prop="overlaies">
          <el-select
            v-model="formData.overlaies"
            placeholder="请选择要叠加的条件"
            multiple
            collapse-tags
            clearable
          >
            <el-option
              v-for="(item, idx) in overlayConditions"
              :key="idx"
              :label="item.description"
              :value="item.variable"
            />
          </el-select>
        </el-form-item>
      </el-form>
    </div>
    <div class="data-list" flex-1 of-auto pl-15>
      <div v-for="(scp, scp_idx) in scopes" :key="scp_idx">
        <div class="scope-title" h-38 lh-38 m-y-12 fs-14 fw-400>{{ scp.name }}</div>
        <el-checkbox-group v-model="choicesMap[scp.level1Code]">
          <el-checkbox
            v-for="(item, idx) in scp.children"
            :key="idx"
            :label="item.name + item.code"
            :value="item.code"
          />
        </el-checkbox-group>
      </div>
    </div>
  </div>
</template>

<style scoped>
.asset-scope-setting {
  background-color: var(--g-block-bg-2);
  .data-list {
    .scope-title {
      color: var(--g-text-color-2);
    }
  }
  :deep() {
    .el-form-item__label {
      color: var(--g-text-color-2) !important;
    }
    .el-checkbox {
      width: 150px;
      height: 38px !important;
    }
  }
}
</style>
